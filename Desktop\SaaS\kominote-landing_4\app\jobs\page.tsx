"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Search,
  Briefcase,
  MapPin,
  Building,
  Filter,
  ChevronDown,
  Bell,
  ArrowRight,
  BookmarkPlus,
  Share2,
  Star,
  Banknote,
  GraduationCap,
  Code,
  ShoppingBag,
  Utensils,
  Plane,
  Wrench,
  Bookmark,
  ArrowUpRight,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

function HeartPulse(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
      <path d="M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27" />
    </svg>
  )
}

function Leaf(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z" />
      <path d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12" />
    </svg>
  )
}

function Home(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  )
}

export default function JobsPage() {
  return (
    <div>
      {/* Jobs page content */}
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
          <div className="container flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-8">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <div className="hidden md:flex relative w-full max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search jobs, companies, or skills..."
                  className="pl-10 pr-4 py-2 w-full border-red-200 focus:border-red-500"
                />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-red-600 relative">
                <Bell className="h-5 w-5" />
                <span className="sr-only">Notifications</span>
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-600">
                  3
                </Badge>
              </Button>
              <div className="flex items-center gap-2">
                <Button variant="ghost" className="text-gray-600 hover:text-red-600">
                  <div className="h-8 w-8 rounded-full bg-red-100 mr-2 overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=32&width=32"
                      width={32}
                      height={32}
                      alt="Profile"
                      className="object-cover"
                    />
                  </div>
                  <span className="hidden md:inline">Sarah Johnson</span>
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </div>
          <div className="container px-4 py-2 border-t border-gray-100">
            <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
              <Link
                href="/jobs"
                className="text-sm font-medium whitespace-nowrap text-red-600 border-b-2 border-red-600 pb-1"
              >
                Browse Jobs
              </Link>
              <Link
                href="/jobs/saved"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-red-600"
              >
                Saved Jobs
              </Link>
              <Link
                href="/jobs/applications"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-red-600"
              >
                My Applications
              </Link>
              <Link
                href="/jobs/profile"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-red-600"
              >
                Career Profile
              </Link>
              <Link
                href="/jobs/post"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-red-600"
              >
                Post a Job
              </Link>
            </nav>
          </div>
        </header>

        <main>
          {/* Hero Banner */}
          <section className="relative bg-gradient-to-r from-red-600 to-rose-500 text-white py-12 md:py-20">
            <div className="container px-4 md:px-6">
              <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                <div className="space-y-4">
                  <h1 className="text-3xl md:text-5xl font-bold tracking-tighter">Find Your Dream Job in Seychelles</h1>
                  <p className="text-lg md:text-xl text-red-50">
                    Discover opportunities across industries, connect with top employers, and take the next step in your
                    career journey.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button className="bg-white text-red-600 hover:bg-red-50">Browse Jobs</Button>
                    <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white/10">
                      <Briefcase className="mr-2 h-4 w-4" /> Upload Resume
                    </Button>
                  </div>
                </div>
                <div className="relative h-[300px] lg:h-[400px] rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    fill
                    alt="Career opportunities"
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Job Search */}
          <section className="py-8 bg-white">
            <div className="container px-4 md:px-6">
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <h2 className="text-xl font-bold mb-4">Find Your Perfect Job</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="search"
                      placeholder="Job title, keywords, or company"
                      className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-red-500"
                    />
                  </div>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Location (Island, City)"
                      className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-red-500"
                    />
                  </div>
                  <Button className="bg-red-600 hover:bg-red-700">Search Jobs</Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-4">
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">Full-time</Badge>
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">Part-time</Badge>
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">Remote</Badge>
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">Entry Level</Badge>
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">Experienced</Badge>
                  <Badge className="bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer">
                    <Filter className="h-3 w-3 mr-1" /> More Filters
                  </Badge>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Jobs */}
          <section className="py-8 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Featured Jobs</h2>
                <div className="flex items-center gap-4">
                  <Button variant="outline" size="sm" className="gap-2 border-red-200 text-red-600 hover:bg-red-50">
                    <Filter className="h-4 w-4" /> Filter
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <FeaturedJobCard
                  title="Senior Hotel Manager"
                  company="Seychelles Luxury Resorts"
                  location="Praslin Island"
                  salary="SCR 45,000 - 60,000 monthly"
                  type="Full-time"
                  posted="2 days ago"
                  logo="/placeholder.svg?height=80&width=80"
                  isFeatured={true}
                />
                <FeaturedJobCard
                  title="Marine Conservation Specialist"
                  company="Ocean Preservation Foundation"
                  location="Mahé"
                  salary="SCR 35,000 - 45,000 monthly"
                  type="Full-time"
                  posted="3 days ago"
                  logo="/placeholder.svg?height=80&width=80"
                  isFeatured={true}
                />
                <FeaturedJobCard
                  title="Tourism Marketing Director"
                  company="Seychelles Tourism Board"
                  location="Victoria, Mahé"
                  salary="SCR 50,000 - 65,000 monthly"
                  type="Full-time"
                  posted="1 day ago"
                  logo="/placeholder.svg?height=80&width=80"
                  isFeatured={true}
                />
              </div>
            </div>
          </section>

          {/* Job Categories */}
          <section className="py-12 bg-gray-50">
            <div className="container px-4 md:px-6">
              <h2 className="text-2xl font-bold mb-8">Browse by Category</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-4">
                <CategoryCard title="Hospitality" icon={<Utensils className="h-8 w-8 text-red-600" />} count={42} />
                <CategoryCard title="Tourism" icon={<Plane className="h-8 w-8 text-red-600" />} count={38} />
                <CategoryCard title="IT & Tech" icon={<Code className="h-8 w-8 text-red-600" />} count={24} />
                <CategoryCard title="Healthcare" icon={<HeartPulse className="h-8 w-8 text-red-600" />} count={31} />
                <CategoryCard title="Education" icon={<GraduationCap className="h-8 w-8 text-red-600" />} count={27} />
                <CategoryCard title="Finance" icon={<Banknote className="h-8 w-8 text-red-600" />} count={19} />
                <CategoryCard title="Retail" icon={<ShoppingBag className="h-8 w-8 text-red-600" />} count={15} />
                <CategoryCard title="Skilled Trades" icon={<Wrench className="h-8 w-8 text-red-600" />} count={12} />
              </div>
            </div>
          </section>

          {/* Recent Jobs */}
          <section className="py-12 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Recent Job Listings</h2>
                <Link
                  href="/jobs/all"
                  className="text-red-600 hover:text-red-700 text-sm font-medium flex items-center"
                >
                  View All <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>

              <Tabs defaultValue="all" className="mb-8">
                <TabsList className="grid grid-cols-5 md:w-[500px]">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="full-time">Full-time</TabsTrigger>
                  <TabsTrigger value="part-time">Part-time</TabsTrigger>
                  <TabsTrigger value="remote">Remote</TabsTrigger>
                  <TabsTrigger value="contract">Contract</TabsTrigger>
                </TabsList>
                <TabsContent value="all" className="mt-6">
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((job) => (
                      <JobCard key={job} />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="full-time" className="mt-6">
                  <div className="space-y-4">
                    {[1, 2, 3].map((job) => (
                      <JobCard key={job} type="Full-time" />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="part-time" className="mt-6">
                  <div className="space-y-4">
                    {[1, 2].map((job) => (
                      <JobCard key={job} type="Part-time" />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="remote" className="mt-6">
                  <div className="space-y-4">
                    {[1].map((job) => (
                      <JobCard key={job} type="Remote" />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="contract" className="mt-6">
                  <div className="space-y-4">
                    {[1, 2].map((job) => (
                      <JobCard key={job} type="Contract" />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-center mt-8">
                <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                  Load More Jobs
                </Button>
              </div>
            </div>
          </section>

          {/* Top Employers */}
          <section className="py-12 bg-red-50">
            <div className="container px-4 md:px-6">
              <h2 className="text-2xl font-bold mb-8">Top Employers</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                {[
                  {
                    name: "Seychelles Tourism Board",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 12,
                    rating: 4.8,
                  },
                  {
                    name: "Eden Island Development",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 8,
                    rating: 4.6,
                  },
                  {
                    name: "Seychelles Hospital",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 15,
                    rating: 4.5,
                  },
                  {
                    name: "Air Seychelles",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 7,
                    rating: 4.7,
                  },
                  {
                    name: "Seychelles Commercial Bank",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 5,
                    rating: 4.9,
                  },
                  {
                    name: "Four Seasons Resort",
                    logo: "/placeholder.svg?height=80&width=80",
                    jobs: 10,
                    rating: 4.7,
                  },
                ].map((employer, index) => (
                  <EmployerCard
                    key={index}
                    name={employer.name}
                    logo={employer.logo}
                    jobs={employer.jobs}
                    rating={employer.rating}
                  />
                ))}
              </div>
            </div>
          </section>

          {/* Career Resources */}
          <section className="py-12 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Career Resources</h2>
                <Link
                  href="/jobs/resources"
                  className="text-red-600 hover:text-red-700 text-sm font-medium flex items-center"
                >
                  View All Resources <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-gray-200 hover:border-red-200 transition-colors">
                  <CardHeader>
                    <CardTitle>Resume Writing Tips</CardTitle>
                    <CardDescription>
                      Learn how to create a standout resume that gets noticed by employers
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative h-40 rounded-md overflow-hidden mb-4">
                      <Image
                        src="/placeholder.svg?height=160&width=320"
                        fill
                        alt="Resume writing"
                        className="object-cover"
                      />
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      A well-crafted resume is your ticket to landing an interview. Learn the essential elements of a
                      compelling resume, including formatting tips, content strategies, and common mistakes to avoid.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-red-600 hover:bg-red-700">
                      Read Article <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>

                <Card className="border-gray-200 hover:border-red-200 transition-colors">
                  <CardHeader>
                    <CardTitle>Interview Preparation Guide</CardTitle>
                    <CardDescription>Strategies to help you ace your next job interview</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative h-40 rounded-md overflow-hidden mb-4">
                      <Image
                        src="/placeholder.svg?height=160&width=320"
                        fill
                        alt="Interview preparation"
                        className="object-cover"
                      />
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      Interviews can be nerve-wracking, but proper preparation can make all the difference. Discover
                      techniques for researching companies, answering common questions, and making a lasting impression.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-red-600 hover:bg-red-700">
                      Read Article <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>

                <Card className="border-gray-200 hover:border-red-200 transition-colors">
                  <CardHeader>
                    <CardTitle>Networking in Seychelles</CardTitle>
                    <CardDescription>Building professional connections in a small island nation</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative h-40 rounded-md overflow-hidden mb-4">
                      <Image
                        src="/placeholder.svg?height=160&width=320"
                        fill
                        alt="Networking"
                        className="object-cover"
                      />
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      In Seychelles' close-knit professional community, networking is crucial for career advancement.
                      Learn effective strategies for building meaningful connections and leveraging them in your job
                      search.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-red-600 hover:bg-red-700">
                      Read Article <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </section>

          {/* Job Alerts CTA */}
          <section className="py-12 bg-gradient-to-r from-red-600 to-rose-500 text-white">
            <div className="container px-4 md:px-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold">Never Miss an Opportunity</h2>
                  <p className="text-red-50">
                    Set up personalized job alerts and be the first to know when your dream job becomes available. Get
                    notifications tailored to your skills, experience, and preferences.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-red-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Customized job recommendations</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-red-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Daily or weekly email notifications</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-red-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Filter by location, salary, and job type</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-red-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Easy application process with one click</span>
                    </li>
                  </ul>
                  <Button className="bg-white text-red-600 hover:bg-red-50 mt-4">
                    <Bell className="mr-2 h-4 w-4" /> Create Job Alert
                  </Button>
                </div>
                <div className="relative h-[300px] rounded-lg overflow-hidden">
                  <Image src="/placeholder.svg?height=400&width=600" fill alt="Job alerts" className="object-cover" />
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t py-12">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <Link href="/" className="flex items-center space-x-2">
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                    Kominote
                  </span>
                </Link>
                <p className="text-sm text-gray-600">
                  Seychelles' premier job platform connecting employers with talented professionals.
                </p>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">For Job Seekers</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Browse Jobs
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Career Resources
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Resume Builder
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Job Alerts
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">For Employers</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Post a Job
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Browse Candidates
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Recruitment Solutions
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Pricing
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">Help & Support</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      FAQs
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Contact Us
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Terms & Conditions
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-red-600">
                      Privacy Policy
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-gray-600">
                &copy; {new Date().getFullYear()} Kominote Jobs. All rights reserved.
              </p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <Link href="#" className="text-gray-600 hover:text-red-600">
                  Terms of Service
                </Link>
                <Link href="#" className="text-gray-600 hover:text-red-600">
                  Privacy Policy
                </Link>
                <Link href="#" className="text-gray-600 hover:text-red-600">
                  Cookie Policy
                </Link>
              </div>
            </div>
          </div>
        </footer>

        {/* Side Navigation */}
        <FeatureNavigation currentFeature="jobs" colorScheme="red" />

        {/* Messenger - with light variant for visibility on light backgrounds */}
        <Messenger variant="light" />
      </div>
    </div>
  )
}

// Featured Job Card Component
function FeaturedJobCard({ title, company, location, salary, type, posted, logo, isFeatured }) {
  return (
    <Card className="overflow-hidden border-red-200 hover:border-red-300 transition-colors">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-12 w-12 rounded-md bg-gray-100 overflow-hidden relative">
              <Image src={logo || "/placeholder.svg"} fill alt={company} className="object-cover" />
            </div>
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              <CardDescription>{company}</CardDescription>
            </div>
          </div>
          {isFeatured && (
            <Badge className="bg-yellow-500 flex items-center gap-1">
              <Star className="h-3 w-3 fill-white" /> Featured
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2 space-y-2">
        <div className="flex items-center gap-1 text-gray-500">
          <MapPin className="h-4 w-4" />
          <span className="text-sm">{location}</span>
        </div>
        <div className="flex items-center gap-1 text-gray-500">
          <Banknote className="h-4 w-4" />
          <span className="text-sm">{salary}</span>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-red-100 text-red-600 hover:bg-red-200">{type}</Badge>
          <span className="text-xs text-gray-500">Posted {posted}</span>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm" className="border-red-200 text-red-600 hover:bg-red-50">
          <Bookmark className="h-4 w-4 mr-1" /> Save
        </Button>
        <Button className="bg-red-600 hover:bg-red-700">Apply Now</Button>
      </CardFooter>
    </Card>
  )
}

// Category Card Component
function CategoryCard({ title, icon, count }) {
  return (
    <Link href={`/jobs/category/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center text-center transition-all duration-200 hover:shadow-md hover:bg-red-50">
        <div className="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center mb-4 group-hover:bg-red-200 transition-colors">
          {icon}
        </div>
        <h3 className="font-medium text-gray-800">{title}</h3>
        <span className="text-xs text-gray-500 mt-1">{count} jobs</span>
      </div>
    </Link>
  )
}

// Job Card Component
function JobCard({ type = "Full-time" }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:border-red-300 transition-colors">
      <div className="flex items-start gap-4">
        <div className="h-12 w-12 rounded-md bg-gray-100 overflow-hidden relative shrink-0">
          <Image src="/placeholder.svg?height=48&width=48" fill alt="Company logo" className="object-cover" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <h3 className="font-bold text-lg truncate">Marketing Manager</h3>
            <div className="flex items-center gap-2">
              <Badge className="bg-red-100 text-red-600 hover:bg-red-200">{type}</Badge>
              <span className="text-xs text-gray-500">Posted 4 days ago</span>
            </div>
          </div>
          <div className="flex items-center gap-1 text-gray-500 mt-1">
            <Building className="h-4 w-4" />
            <span className="text-sm">Seychelles Marketing Agency</span>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 mt-2">
            <div className="flex items-center gap-1 text-gray-500">
              <MapPin className="h-4 w-4" />
              <span className="text-sm">Victoria, Mahé</span>
            </div>
            <div className="flex items-center gap-1 text-gray-500">
              <Banknote className="h-4 w-4" />
              <span className="text-sm">SCR 30,000 - 40,000 monthly</span>
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-3 line-clamp-2">
            We are seeking an experienced Marketing Manager to lead our team and develop comprehensive marketing
            strategies for our clients in the tourism and hospitality sectors.
          </p>
          <div className="flex flex-wrap gap-2 mt-3">
            <Badge className="bg-gray-100 text-gray-600">Marketing</Badge>
            <Badge className="bg-gray-100 text-gray-600">Strategy</Badge>
            <Badge className="bg-gray-100 text-gray-600">Tourism</Badge>
            <Badge className="bg-gray-100 text-gray-600">5+ Years</Badge>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="text-gray-500 hover:text-red-600">
            <BookmarkPlus className="h-4 w-4 mr-1" /> Save
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-500 hover:text-red-600">
            <Share2 className="h-4 w-4 mr-1" /> Share
          </Button>
        </div>
        <Button className="bg-red-600 hover:bg-red-700">Apply Now</Button>
      </div>
    </div>
  )
}

// Employer Card Component
function EmployerCard({ name, logo, jobs, rating }) {
  return (
    <Link href={`/jobs/company/${name.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:border-red-300 transition-colors flex flex-col items-center text-center">
        <div className="h-16 w-16 rounded-md bg-gray-100 overflow-hidden relative mb-3">
          <Image src={logo || "/placeholder.svg"} fill alt={name} className="object-cover" />
        </div>
        <h3 className="font-medium text-gray-800 line-clamp-1">{name}</h3>
        <div className="flex items-center gap-1 mt-1">
          <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
          <span className="text-xs text-gray-500">
            {rating} ({Math.floor(rating * 10)} reviews)
          </span>
        </div>
        <Badge className="mt-2 bg-red-100 text-red-600">{jobs} open jobs</Badge>
        <Button variant="outline" size="sm" className="w-full mt-3 border-red-200 text-red-600 hover:bg-red-50">
          View Company
        </Button>
      </div>
    </Link>
  )
}

