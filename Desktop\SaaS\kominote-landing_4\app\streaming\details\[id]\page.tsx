import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Play, Plus, ThumbsUp, <PERSON>hare2, ArrowLeft, Star } from "lucide-react"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function ContentDetailsPage({ params }) {
  const { id } = params

  // This would normally come from an API or database
  // For demo purposes, we'll create mock data based on the ID
  const content = getContentDetails(id)

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link href="/streaming" className="text-sm font-medium text-gray-300 hover:text-white">
                Home
              </Link>
              <Link href="/streaming/series" className="text-sm font-medium text-gray-300 hover:text-white">
                Series
              </Link>
              <Link href="/streaming/movies" className="text-sm font-medium text-gray-300 hover:text-white">
                Movies
              </Link>
              <Link href="/streaming/new" className="text-sm font-medium text-gray-300 hover:text-white">
                New & Popular
              </Link>
              <Link href="/streaming/mylist" className="text-sm font-medium text-gray-300 hover:text-white">
                My List
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <UserNav />
          </div>
        </div>
      </header>

      <main>
        {/* Back Button */}
        <div className="container px-4 py-4">
          <Link href="/streaming">
            <Button variant="ghost" className="text-gray-300 hover:text-white">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Browse
            </Button>
          </Link>
        </div>

        {/* Hero Banner */}
        <section className="relative h-[60vh] w-full">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            fill
            alt={content.title}
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-12 space-y-4">
            <div className="max-w-lg">
              <Badge className="mb-4 bg-blue-600">{content.type === "series" ? "SERIES" : "MOVIE"}</Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{content.title}</h1>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 mr-1" />
                  <span className="text-sm">{content.rating}/10</span>
                </div>
                <span className="text-sm text-gray-300">{content.year}</span>
                <span className="text-sm text-gray-300">{content.duration}</span>
                <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">
                  {content.genre}
                </Badge>
              </div>
              <p className="text-base text-gray-200 mb-6">{content.description}</p>
              <div className="flex  gap-3">
                <Link href={`/streaming/watch/${id}`}>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8">
                    <Play className="mr-2 h-5 w-5" /> Play
                  </Button>
                </Link>
                <Button variant="outline" className="bg-gray-800/60 text-white border-gray-600 hover:bg-gray-700">
                  <Plus className="mr-2 h-5 w-5" /> My List
                </Button>
                <Button variant="outline" className="bg-gray-800/60 text-white border-gray-600 hover:bg-gray-700">
                  <ThumbsUp className="mr-2 h-5 w-5" /> Rate
                </Button>
                <Button variant="outline" className="bg-gray-800/60 text-white border-gray-600 hover:bg-gray-700">
                  <Share2 className="mr-2 h-5 w-5" /> Share
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Content Details */}
        <section className="py-8 px-4 md:px-12">
          <Tabs defaultValue="episodes" className="w-full">
            <TabsList className="bg-gray-800/60 mb-6">
              {content.type === "series" && (
                <TabsTrigger value="episodes" className="data-[state=active]:bg-blue-600">
                  Episodes
                </TabsTrigger>
              )}
              <TabsTrigger value="details" className="data-[state=active]:bg-blue-600">
                Details
              </TabsTrigger>
              <TabsTrigger value="related" className="data-[state=active]:bg-blue-600">
                Related
              </TabsTrigger>
            </TabsList>

            {content.type === "series" && (
              <TabsContent value="episodes" className="mt-0">
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold">Episodes</h2>
                    {content.seasons && content.seasons.length > 1 && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Season:</span>
                        <select className="bg-gray-800 border border-gray-700 rounded-md text-sm p-1">
                          {Array.from({ length: content.seasons }, (_, i) => (
                            <option key={i + 1} value={i + 1}>
                              Season {i + 1}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    {Array.from({ length: content.episodes || 0 }, (_, i) => (
                      <div
                        key={i}
                        className="flex flex-col md:flex-row gap-4 p-4 rounded-lg bg-gray-800/40 hover:bg-gray-800/60 transition-colors"
                      >
                        <div className="relative w-full md:w-64 aspect-video rounded-md overflow-hidden">
                          <Image
                            src="/placeholder.svg?height=180&width=320"
                            fill
                            alt={`Episode ${i + 1}`}
                            className="object-cover"
                          />
                          <Link
                            href={`/streaming/watch/${id}-s1e${i + 1}`}
                            className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 hover:opacity-100 transition-opacity"
                          >
                            <Button size="icon" className="h-12 w-12 rounded-full bg-blue-600/80 hover:bg-blue-700">
                              <Play className="h-6 w-6" />
                            </Button>
                          </Link>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">Episode {i + 1}</h3>
                            <span className="text-sm text-gray-400">45 min</span>
                          </div>
                          <h4 className="text-base font-medium text-gray-300 mb-2">
                            {content.episodeTitles ? content.episodeTitles[i] : `${content.title} - Episode ${i + 1}`}
                          </h4>
                          <p className="text-sm text-gray-400">
                            {content.episodeDescriptions
                              ? content.episodeDescriptions[i]
                              : `This episode continues the ${content.genre.toLowerCase()} journey through the beautiful landscapes and culture of Seychelles.`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            )}

            <TabsContent value="details" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2 space-y-6">
                  <div>
                    <h2 className="text-xl font-bold mb-4">About {content.title}</h2>
                    <p className="text-gray-300">{content.fullDescription || content.description}</p>
                  </div>

                  {content.creators && (
                    <div>
                      <h3 className="text-lg font-medium mb-2">Creators</h3>
                      <p className="text-gray-300">{content.creators}</p>
                    </div>
                  )}

                  {content.cast && (
                    <div>
                      <h3 className="text-lg font-medium mb-2">Cast</h3>
                      <p className="text-gray-300">{content.cast}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Information</h3>
                    <dl className="space-y-2">
                      <div className="flex">
                        <dt className="w-24 text-gray-400">Genre:</dt>
                        <dd className="text-gray-300">{content.genre}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-24 text-gray-400">Released:</dt>
                        <dd className="text-gray-300">{content.year}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-24 text-gray-400">Duration:</dt>
                        <dd className="text-gray-300">{content.duration}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-24 text-gray-400">Rating:</dt>
                        <dd className="text-gray-300">{content.rating}/10</dd>
                      </div>
                      {content.type === "series" && (
                        <>
                          <div className="flex">
                            <dt className="w-24 text-gray-400">Seasons:</dt>
                            <dd className="text-gray-300">{content.seasons}</dd>
                          </div>
                          <div className="flex">
                            <dt className="w-24 text-gray-400">Episodes:</dt>
                            <dd className="text-gray-300">{content.episodes}</dd>
                          </div>
                        </>
                      )}
                    </dl>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {content.tags &&
                        content.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="bg-gray-800 hover:bg-gray-700">
                            {tag}
                          </Badge>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="related" className="mt-0">
              <div className="mb-10">
                <h2 className="text-xl font-bold mb-4">More Like This</h2>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {content.related &&
                    content.related.map((item, index) => (
                      <Link key={index} href={`/streaming/details/${item.id}`}>
                        <div className="relative aspect-video overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105">
                          <Image
                            src="/placeholder.svg?height=180&width=320"
                            width={320}
                            height={180}
                            alt={item.title}
                            className="object-cover w-full h-full"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
                          <div className="absolute bottom-0 left-0 right-0 p-3">
                            <h3 className="text-sm font-medium">{item.title}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
                                {item.type === "series" ? "SERIES" : "MOVIE"}
                              </Badge>
                              <span className="text-xs text-gray-400">{item.genre}</span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container px-4 md:px-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-400">Seychelles' premier streaming platform for local content</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Browse</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/streaming/series" className="text-sm text-gray-400 hover:text-white">
                    Series
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/movies" className="text-sm text-gray-400 hover:text-white">
                    Movies
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/new" className="text-sm text-gray-400 hover:text-white">
                    New & Popular
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/mylist" className="text-sm text-gray-400 hover:text-white">
                    My List
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Help</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Devices
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Terms of Use
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Cookie Preferences
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Corporate Information
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-xs text-gray-400">
              &copy; {new Date().getFullYear()} Kominote Streaming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="streaming" colorScheme="blue" />
    </div>
  )
}

// Helper function to generate content details based on ID
function getContentDetails(id) {
  // Default content structure
  const defaultContent = {
    title: "Content Title",
    type: "movie",
    genre: "Documentary",
    year: "2023",
    duration: "1h 45m",
    rating: "8.5",
    description: "A captivating story about the beautiful islands of Seychelles.",
    fullDescription:
      "A captivating story about the beautiful islands of Seychelles, showcasing the natural beauty and rich cultural heritage of this island paradise.",
    creators: "John Doe, Jane Smith",
    cast: "Local talent and community members",
    tags: ["Seychelles", "Island", "Culture", "Nature"],
    related: [
      { id: "coral-reef-chronicles", title: "Coral Reef Chronicles", type: "series", genre: "Documentary" },
      { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
      { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
      { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
      { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
      { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
    ],
  }

  // Content specific to different IDs
  const contentMap = {
    "seychelles-untold": {
      title: "Seychelles Untold",
      type: "series",
      genre: "Documentary",
      year: "2023",
      duration: "45m per episode",
      rating: "9.2",
      description: "Discover the hidden stories of Seychelles in this captivating documentary series.",
      fullDescription:
        "Discover the hidden stories of Seychelles in this captivating documentary series. From ancient traditions to modern challenges, explore the authentic culture of our islands through the eyes of local storytellers and historians.",
      creators: "Marie Dubois, Jean-Paul Larue",
      cast: "Local historians, community elders, and cultural experts",
      seasons: 2,
      episodes: 8,
      episodeTitles: [
        "Origins",
        "Island Formation",
        "First Settlers",
        "Colonial Era",
        "Path to Independence",
        "Modern Challenges",
        "Conservation Efforts",
        "Future Visions",
      ],
      episodeDescriptions: [
        "Explore the geological origins of the Seychelles archipelago and how these granite islands emerged from the Indian Ocean.",
        "Discover how the unique island formations shaped the biodiversity and ecosystems found nowhere else on Earth.",
        "Learn about the first human settlers who arrived on these pristine shores and their early struggles and triumphs.",
        "Examine the colonial period and how different powers influenced the culture and development of Seychelles.",
        "Follow the journey to independence and the birth of a new nation in the Indian Ocean.",
        "Understand the modern challenges facing this small island nation, from climate change to economic development.",
        "Witness the groundbreaking conservation efforts that are protecting Seychelles' unique natural heritage.",
        "Look to the future as Seychellois visionaries share their hopes and plans for their island home.",
      ],
      tags: ["Documentary", "History", "Culture", "Environment", "Seychelles", "Island Nation"],
    },
    "island-paradise": {
      title: "Island Paradise",
      type: "movie",
      genre: "Documentary",
      year: "2022",
      duration: "1h 52m",
      rating: "8.7",
      description: "Experience the breathtaking beauty of Seychelles in this stunning documentary.",
      fullDescription:
        "Experience the breathtaking beauty of Seychelles in this stunning documentary showcasing the islands' natural wonders and vibrant culture. From pristine beaches to lush mountain forests, discover why Seychelles is considered one of Earth's last paradises.",
      creators: "Thomas Laurent",
      cast: "Local guides, marine biologists, and conservation experts",
      tags: ["Nature", "Travel", "Conservation", "Marine Life", "Beaches", "Wildlife"],
    },
    "coral-reef-chronicles": {
      title: "Coral Reef Chronicles",
      type: "series",
      genre: "Documentary",
      year: "2021",
      duration: "40m per episode",
      rating: "9.0",
      description: "Dive into the vibrant underwater world of Seychelles' coral reefs.",
      fullDescription:
        "Dive into the vibrant underwater world of Seychelles' coral reefs in this breathtaking series. Each episode explores different aspects of reef ecology, the challenges facing these delicate ecosystems, and the dedicated people working to protect them.",
      creators: "Marine Conservation Society Seychelles",
      cast: "Marine biologists, conservationists, and local divers",
      seasons: 1,
      episodes: 6,
      tags: ["Marine", "Conservation", "Underwater", "Ecology", "Coral", "Ocean"],
    },
  }

  // Return specific content if it exists, otherwise return default with the ID as title
  return (
    contentMap[id] || {
      ...defaultContent,
      title: id
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
    }
  )
}

