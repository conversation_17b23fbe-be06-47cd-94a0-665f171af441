import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, ShoppingBag, Users, Calendar, Briefcase, ChevronRight, ArrowRight } from "lucide-react"
import { Header } from "@/components/header"

export default function LandingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <Header />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-blue-50 to-white">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    Seychelles' All-in-One Digital Hub
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Entertainment, commerce, and connection in one place. Discover the digital heart of Seychelles.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-teal-500 text-white">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="lg">
                    Learn More
                  </Button>
                </div>
              </div>
              <Image
                src="/placeholder.svg?height=550&width=550"
                width={550}
                height={550}
                alt="Kominote Digital Hub"
                className="mx-auto aspect-video overflow-hidden rounded-xl object-cover sm:w-full lg:order-last"
              />
            </div>
          </div>
        </section>

        {/* Pillars Overview */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Five Pillars, One Platform</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Everything you need in one place. Kominote brings together the best of digital life in Seychelles.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-8 mt-16">
              <div className="flex flex-col items-center space-y-2 p-6 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors">
                <div className="p-3 rounded-full bg-blue-100">
                  <Play className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold">Streaming</h3>
                <p className="text-sm text-center text-muted-foreground">
                  Watch local content and international favorites
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 p-6 rounded-lg bg-emerald-50 hover:bg-emerald-100 transition-colors">
                <div className="p-3 rounded-full bg-emerald-100">
                  <ShoppingBag className="h-6 w-6 text-emerald-600" />
                </div>
                <h3 className="text-xl font-bold">Shopping</h3>
                <p className="text-sm text-center text-muted-foreground">Discover local products and services</p>
              </div>
              <div className="flex flex-col items-center space-y-2 p-6 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors">
                <div className="p-3 rounded-full bg-purple-100">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold">Community</h3>
                <p className="text-sm text-center text-muted-foreground">
                  Connect with people who share your interests
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 p-6 rounded-lg bg-amber-50 hover:bg-amber-100 transition-colors">
                <div className="p-3 rounded-full bg-amber-100">
                  <Calendar className="h-6 w-6 text-amber-600" />
                </div>
                <h3 className="text-xl font-bold">Events</h3>
                <p className="text-sm text-center text-muted-foreground">Discover and attend local happenings</p>
              </div>
              <div className="flex flex-col items-center space-y-2 p-6 rounded-lg bg-red-50 hover:bg-red-100 transition-colors">
                <div className="p-3 rounded-full bg-red-100">
                  <Briefcase className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-xl font-bold">Jobs</h3>
                <p className="text-sm text-center text-muted-foreground">Find opportunities and advance your career</p>
              </div>
            </div>
          </div>
        </section>

        {/* Streaming Section */}
        <section id="streaming" className="w-full py-12 md:py-24 lg:py-32 bg-blue-50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-blue-100 px-3 py-1 text-sm text-blue-600">Streaming</div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Entertainment at Your Fingertips
                </h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed">
                  Watch local Seychellois content, international movies, TV shows, and live events. Stream anywhere,
                  anytime on any device.
                </p>
                <ul className="grid gap-2">
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-blue-600" />
                    <span>Local Seychellois productions</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-blue-600" />
                    <span>International movies and TV shows</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-blue-600" />
                    <span>Live events and sports</span>
                  </li>
                </ul>
                <Button className="bg-blue-600 hover:bg-blue-700">Start Watching</Button>
              </div>
              <div className="relative aspect-video overflow-hidden rounded-xl">
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  width={600}
                  height={400}
                  alt="Streaming content"
                  className="object-cover w-full h-full"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="rounded-full bg-white/90 p-4 shadow-lg">
                    <Play className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Shopping Section */}
        <section id="shopping" className="w-full py-12 md:py-24 lg:py-32 bg-white">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-2 gap-4">
                  <Image
                    src="/placeholder.svg?height=300&width=300"
                    width={300}
                    height={300}
                    alt="Local products"
                    className="rounded-lg object-cover w-full h-full"
                  />
                  <Image
                    src="/placeholder.svg?height=300&width=300"
                    width={300}
                    height={300}
                    alt="Shopping experience"
                    className="rounded-lg object-cover w-full h-full"
                  />
                  <Image
                    src="/placeholder.svg?height=300&width=300"
                    width={300}
                    height={300}
                    alt="Local marketplace"
                    className="rounded-lg object-cover w-full h-full"
                  />
                  <Image
                    src="/placeholder.svg?height=300&width=300"
                    width={300}
                    height={300}
                    alt="Artisan crafts"
                    className="rounded-lg object-cover w-full h-full"
                  />
                </div>
              </div>
              <div className="space-y-4 order-1 lg:order-2">
                <div className="inline-block rounded-lg bg-emerald-100 px-3 py-1 text-sm text-emerald-600">
                  Shopping
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Discover Local Treasures</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed">
                  Shop from local businesses, artisans, and service providers. Support the Seychellois economy while
                  finding unique products.
                </p>
                <ul className="grid gap-2">
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-emerald-600" />
                    <span>Local artisan crafts and products</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-emerald-600" />
                    <span>Services from trusted providers</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-emerald-600" />
                    <span>Secure payments and delivery</span>
                  </li>
                </ul>
                <Button className="bg-emerald-600 hover:bg-emerald-700">Start Shopping</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Community Section */}
        <section id="community" className="w-full py-12 md:py-24 lg:py-32 bg-purple-50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-purple-100 px-3 py-1 text-sm text-purple-600">Community</div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Connect with Your People</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed">
                  Join groups, forums, and discussions centered around your interests. Build meaningful connections with
                  fellow Seychellois.
                </p>
                <ul className="grid gap-2">
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-purple-600" />
                    <span>Interest-based groups and forums</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-purple-600" />
                    <span>Local discussions and news</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-purple-600" />
                    <span>Share experiences and knowledge</span>
                  </li>
                </ul>
                <Button className="bg-purple-600 hover:bg-purple-700">Join Communities</Button>
              </div>
              <Image
                src="/placeholder.svg?height=400&width=600"
                width={600}
                height={400}
                alt="Community connections"
                className="rounded-xl object-cover"
              />
            </div>
          </div>
        </section>

        {/* Events Section */}
        <section id="events" className="w-full py-12 md:py-24 lg:py-32 bg-white">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="relative">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    width={600}
                    height={400}
                    alt="Local events"
                    className="rounded-xl object-cover"
                  />
                  <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg">
                    <Calendar className="h-12 w-12 text-amber-600" />
                  </div>
                </div>
              </div>
              <div className="space-y-4 order-1 lg:order-2">
                <div className="inline-block rounded-lg bg-amber-100 px-3 py-1 text-sm text-amber-600">Events</div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Never Miss a Moment</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed">
                  Discover local events, festivals, and gatherings. From cultural celebrations to business networking,
                  find what's happening in Seychelles.
                </p>
                <ul className="grid gap-2">
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-amber-600" />
                    <span>Cultural festivals and celebrations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-amber-600" />
                    <span>Business and networking events</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-amber-600" />
                    <span>Community gatherings and activities</span>
                  </li>
                </ul>
                <Button className="bg-amber-600 hover:bg-amber-700">Explore Events</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Jobs Section */}
        <section id="jobs" className="w-full py-12 md:py-24 lg:py-32 bg-red-50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-red-100 px-3 py-1 text-sm text-red-600">Jobs</div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Advance Your Career</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed">
                  Find job opportunities across Seychelles. Whether you're looking for your first job or your next
                  career move, we've got you covered.
                </p>
                <ul className="grid gap-2">
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-red-600" />
                    <span>Job listings from top employers</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-red-600" />
                    <span>Career advice and resources</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <ChevronRight className="h-4 w-4 text-red-600" />
                    <span>Professional networking opportunities</span>
                  </li>
                </ul>
                <Button className="bg-red-600 hover:bg-red-700">Find Jobs</Button>
              </div>
              <Image
                src="/placeholder.svg?height=400&width=600"
                width={600}
                height={400}
                alt="Career opportunities"
                className="rounded-xl object-cover"
              />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-white to-blue-50">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Why Choose Kominote?</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Kominote brings together everything you need in one seamless digital experience.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-3 md:gap-12">
              <div className="flex flex-col items-center space-y-2 border p-6 rounded-lg shadow-sm">
                <div className="p-3 rounded-full bg-blue-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-blue-600"
                  >
                    <path d="m8 3 4 8 5-5 5 15H2L8 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Made for Seychelles</h3>
                <p className="text-sm text-center text-muted-foreground">
                  Built specifically for the unique needs of Seychellois people and businesses.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border p-6 rounded-lg shadow-sm">
                <div className="p-3 rounded-full bg-blue-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-blue-600"
                  >
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Secure & Reliable</h3>
                <p className="text-sm text-center text-muted-foreground">
                  Your data is protected with industry-leading security practices.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border p-6 rounded-lg shadow-sm">
                <div className="p-3 rounded-full bg-blue-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-blue-600"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Community-Driven</h3>
                <p className="text-sm text-center text-muted-foreground">
                  Built with input from the community, for the community.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-blue-600 to-teal-500 text-white">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Join Kominote Today</h2>
                <p className="max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Be part of Seychelles' digital revolution. Connect, shop, watch, and grow with Kominote.
                </p>
              </div>
              <div className="mx-auto w-full max-w-sm space-y-2">
                <form className="flex flex-col sm:flex-row gap-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex h-10 w-full rounded-md border border-input bg-white px-3 py-2 text-sm text-gray-800 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                  <Button className="bg-white text-blue-600 hover:bg-gray-100">Get Started</Button>
                </form>
                <p className="text-xs">
                  By signing up, you agree to our{" "}
                  <Link href="#" className="underline underline-offset-2">
                    Terms & Conditions
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full border-t bg-background">
        <div className="container flex flex-col gap-6 py-12 px-4 md:px-6 md:flex-row md:justify-between">
          <div className="flex flex-col gap-6 md:w-1/3">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <p className="text-sm text-muted-foreground">
              Seychelles' all-in-one digital hub for entertainment, commerce, and connection.
            </p>
            <div className="flex gap-4">
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </Link>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-4">
            <div className="flex flex-col gap-2">
              <h3 className="text-sm font-medium">Platform</h3>
              <Link href="/streaming" className="text-sm text-muted-foreground hover:text-foreground">
                Streaming
              </Link>
              <Link href="/shopping" className="text-sm text-muted-foreground hover:text-foreground">
                Shopping
              </Link>
              <Link href="/community" className="text-sm text-muted-foreground hover:text-foreground">
                Community
              </Link>
              <Link href="#events" className="text-sm text-muted-foreground hover:text-foreground">
                Events
              </Link>
              <Link href="#jobs" className="text-sm text-muted-foreground hover:text-foreground">
                Jobs
              </Link>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="text-sm font-medium">Company</h3>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                About
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Careers
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Press
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                News
              </Link>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="text-sm font-medium">Resources</h3>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Blog
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Newsletter
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Events
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="text-sm font-medium">Legal</h3>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Terms
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Privacy
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Cookies
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Licenses
              </Link>
            </div>
          </div>
        </div>
        <div className="border-t py-6">
          <div className="container flex flex-col items-center justify-between gap-4 px-4 md:px-6 md:flex-row">
            <p className="text-xs text-muted-foreground">
              &copy; {new Date().getFullYear()} Kominote. All rights reserved.
            </p>
            <p className="text-xs text-muted-foreground">Made with ❤️ in Seychelles</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

