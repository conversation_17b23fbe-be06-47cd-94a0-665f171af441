import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  ShoppingCart,
  Trash2,
  Plus,
  Minus,
  ArrowRight,
  ChevronLeft,
  CreditCard,
  Wallet,
  ShieldCheck,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function CartPage() {
  // In a real app, you would fetch cart data from a state management solution or API
  const cartItems = [
    {
      id: "coconut-bowl-01",
      name: "Handmade Coconut Bowl",
      price: 24.99,
      quantity: 2,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Island Crafts Co.",
    },
    {
      id: "bamboo-straw-01",
      name: "Reusable Bamboo Straws (Set of 8)",
      price: 12.99,
      quantity: 1,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Eco Seychelles",
    },
    {
      id: "palm-plate-01",
      name: "Palm Leaf Plates (Set of 4)",
      price: 29.99,
      quantity: 1,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Island Crafts Co.",
    },
  ]

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => total + item.price * item.quantity, 0)
  const shipping = subtotal > 50 ? 0 : 5.99
  const tax = subtotal * 0.15 // 15% tax
  const total = subtotal + shipping + tax

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Simplified for this page */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
          </div>
        </div>
      </header>

      <main className="container px-4 py-8 md:px-6">
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <Link href="/shopping" className="hover:text-emerald-600">
            Home
          </Link>
          <span className="mx-2">/</span>
          <span className="text-emerald-600 font-medium">Shopping Cart</span>
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-8 flex items-center gap-2">
          <ShoppingCart className="h-6 w-6" />
          Your Shopping Cart
        </h1>

        {cartItems.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-medium">Cart Items ({cartItems.length})</h2>
                    <Button variant="ghost" size="sm" className="text-gray-500 hover:text-red-500">
                      <Trash2 className="h-4 w-4 mr-1" />
                      Clear Cart
                    </Button>
                  </div>

                  <div className="space-y-6">
                    {cartItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex flex-col sm:flex-row gap-4 pb-6 border-b last:border-0 last:pb-0"
                      >
                        <div className="relative h-24 w-24 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                          <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                        </div>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:justify-between gap-2">
                            <div>
                              <h3 className="font-medium text-gray-900">
                                <Link href={`/shopping/product/${item.id}`} className="hover:text-emerald-600">
                                  {item.name}
                                </Link>
                              </h3>
                              <p className="text-sm text-gray-500">Vendor: {item.vendor}</p>
                            </div>
                            <p className="font-medium text-emerald-600">${item.price.toFixed(2)}</p>
                          </div>
                          <div className="flex justify-between items-center mt-4">
                            <div className="flex items-center border rounded-md">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-none border-r"
                                disabled={item.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-10 text-center text-sm">{item.quantity}</span>
                              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-none border-l">
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-0 h-8"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Link href="/shopping">
                    <Button variant="outline" className="w-full">
                      <ChevronLeft className="mr-2 h-4 w-4" />
                      Continue Shopping
                    </Button>
                  </Link>
                </div>
                <div className="flex-1">
                  <Link href="/shopping/checkout">
                    <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                      Proceed to Checkout
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <h2 className="text-lg font-medium mb-4">Order Summary</h2>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping</span>
                      <span className="font-medium">{shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tax (15%)</span>
                      <span className="font-medium">${tax.toFixed(2)}</span>
                    </div>
                    <Separator className="my-3" />
                    <div className="flex justify-between">
                      <span className="font-medium">Total</span>
                      <span className="font-bold text-emerald-600">${total.toFixed(2)}</span>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Link href="/shopping/checkout">
                      <Button className="w-full bg-emerald-600 hover:bg-emerald-700">Proceed to Checkout</Button>
                    </Link>
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <h3 className="text-sm font-medium mb-3">Apply Coupon Code</h3>
                    <div className="flex gap-2">
                      <Input placeholder="Enter coupon code" className="flex-1" />
                      <Button variant="outline" className="border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                        Apply
                      </Button>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <h3 className="text-sm font-medium mb-3">We Accept</h3>
                    <div className="flex flex-wrap gap-2">
                      <div className="h-8 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-gray-600" />
                      </div>
                      <div className="h-8 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <Wallet className="h-4 w-4 text-gray-600" />
                      </div>
                      <div className="h-8 px-2 bg-gray-100 rounded flex items-center justify-center text-xs font-medium">
                        Kominote Pay
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <ShieldCheck className="h-4 w-4 text-emerald-600" />
                      <span>Secure checkout powered by Kominote Pay</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <h2 className="text-lg font-medium mb-4">Shipping Information</h2>
                  <p className="text-sm text-gray-600 mb-4">
                    Free shipping on all orders over $50. Standard delivery takes 3-5 business days.
                  </p>
                  <p className="text-sm text-gray-600">
                    For more information, please visit our{" "}
                    <Link href="#" className="text-emerald-600 hover:underline">
                      shipping policy
                    </Link>{" "}
                    page.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <ShoppingCart className="h-8 w-8 text-gray-400" />
            </div>
            <h2 className="text-xl font-medium mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Looks like you haven't added any products to your cart yet.</p>
            <Link href="/shopping">
              <Button className="bg-emerald-600 hover:bg-emerald-700">Start Shopping</Button>
            </Link>
          </div>
        )}
      </main>

      {/* Footer - Simplified for this page */}
      <footer className="bg-white border-t py-8 mt-12">
        <div className="container px-4 md:px-6">
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger */}
      <Messenger variant="light" />
    </div>
  )
}

