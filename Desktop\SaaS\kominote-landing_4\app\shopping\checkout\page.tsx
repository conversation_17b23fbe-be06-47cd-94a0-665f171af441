import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { ChevronLeft, CreditCard, Wallet, ShieldCheck, Check, MapPin, Truck, Clock } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function CheckoutPage() {
  // In a real app, you would fetch cart data from a state management solution or API
  const cartItems = [
    {
      id: "coconut-bowl-01",
      name: "Handmade Coconut Bowl",
      price: 24.99,
      quantity: 2,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Island Crafts Co.",
    },
    {
      id: "bamboo-straw-01",
      name: "Reusable Bamboo Straws (Set of 8)",
      price: 12.99,
      quantity: 1,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Eco Seychelles",
    },
    {
      id: "palm-plate-01",
      name: "Palm Leaf Plates (Set of 4)",
      price: 29.99,
      quantity: 1,
      image: "/placeholder.svg?height=100&width=100",
      vendor: "Island Crafts Co.",
    },
  ]

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => total + item.price * item.quantity, 0)
  const shipping = subtotal > 50 ? 0 : 5.99
  const tax = subtotal * 0.15 // 15% tax
  const total = subtotal + shipping + tax

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Simplified for this page */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
          </div>
        </div>
      </header>

      <main className="container px-4 py-8 md:px-6">
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <Link href="/shopping" className="hover:text-emerald-600">
            Home
          </Link>
          <span className="mx-2">/</span>
          <Link href="/shopping/cart" className="hover:text-emerald-600">
            Shopping Cart
          </Link>
          <span className="mx-2">/</span>
          <span className="text-emerald-600 font-medium">Checkout</span>
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Shipping Address */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-emerald-600" />
                    Shipping Address
                  </h2>
                  <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700 p-0 h-auto">
                    Use Saved Address
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="first-name">First Name</Label>
                    <Input id="first-name" placeholder="Enter your first name" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="last-name">Last Name</Label>
                    <Input id="last-name" placeholder="Enter your last name" className="mt-1" />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Street Address</Label>
                    <Input id="address" placeholder="Enter your street address" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input id="city" placeholder="Enter your city" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="postal-code">Postal Code</Label>
                    <Input id="postal-code" placeholder="Enter your postal code" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" placeholder="Enter your phone number" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" placeholder="Enter your email address" className="mt-1" />
                  </div>
                  <div className="md:col-span-2">
                    <div className="flex items-center space-x-2 mt-4">
                      <Checkbox id="save-address" />
                      <Label htmlFor="save-address" className="text-sm font-normal">
                        Save this address for future orders
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Method */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <Truck className="h-5 w-5 text-emerald-600" />
                  Delivery Method
                </h2>

                <RadioGroup defaultValue="standard" className="space-y-3">
                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="standard" id="standard" />
                      <Label htmlFor="standard" className="font-medium">
                        Standard Delivery
                      </Label>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}</p>
                      <p className="text-sm text-gray-500">3-5 business days</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="express" id="express" />
                      <Label htmlFor="express" className="font-medium">
                        Express Delivery
                      </Label>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">$12.99</p>
                      <p className="text-sm text-gray-500">1-2 business days</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="pickup" id="pickup" />
                      <Label htmlFor="pickup" className="font-medium">
                        Store Pickup
                      </Label>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Free</p>
                      <p className="text-sm text-gray-500">Available tomorrow</p>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <CreditCard className="h-5 w-5 text-emerald-600" />
                  Payment Method
                </h2>

                <RadioGroup defaultValue="credit-card" className="space-y-3">
                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="credit-card" id="credit-card" />
                      <Label htmlFor="credit-card" className="font-medium">
                        Credit / Debit Card
                      </Label>
                    </div>
                    <div className="flex gap-2">
                      <div className="h-8 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-gray-600" />
                      </div>
                    </div>
                  </div>

                  <div className="ml-6 pl-6 border-l-2 border-gray-100 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <Label htmlFor="card-number">Card Number</Label>
                        <Input id="card-number" placeholder="0000 0000 0000 0000" className="mt-1" />
                      </div>
                      <div>
                        <Label htmlFor="expiry">Expiry Date</Label>
                        <Input id="expiry" placeholder="MM/YY" className="mt-1" />
                      </div>
                      <div>
                        <Label htmlFor="cvv">CVV</Label>
                        <Input id="cvv" placeholder="123" className="mt-1" />
                      </div>
                      <div className="md:col-span-2">
                        <Label htmlFor="card-name">Name on Card</Label>
                        <Input id="card-name" placeholder="Enter name as it appears on card" className="mt-1" />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="save-card" />
                      <Label htmlFor="save-card" className="text-sm font-normal">
                        Save this card for future payments
                      </Label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="kominote-pay" id="kominote-pay" />
                      <Label htmlFor="kominote-pay" className="font-medium">
                        Kominote Pay
                      </Label>
                    </div>
                    <div className="h-8 px-2 bg-gray-100 rounded flex items-center justify-center text-xs font-medium">
                      Kominote Pay
                    </div>
                  </div>

                  <div className="flex items-center justify-between space-x-2 border rounded-md p-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="wallet" id="wallet" />
                      <Label htmlFor="wallet" className="font-medium">
                        Wallet Balance
                      </Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Wallet className="h-4 w-4 text-emerald-600" />
                      <span className="font-medium">$120.50</span>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Order Notes */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium mb-4">Order Notes (Optional)</h2>
                <Textarea
                  placeholder="Add any special instructions or notes about your order here..."
                  className="min-h-[100px]"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Link href="/shopping/cart">
                  <Button variant="outline" className="w-full">
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    Back to Cart
                  </Button>
                </Link>
              </div>
              <div className="flex-1">
                <Link href="/shopping/checkout/confirmation">
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700">Place Order</Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium mb-4">Order Summary</h2>

                <div className="space-y-4 mb-6">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex gap-3">
                      <div className="relative h-16 w-16 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                        <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-900 line-clamp-1">{item.name}</h3>
                        <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                        <p className="text-sm font-medium text-emerald-600 mt-1">
                          ${(item.price * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <Separator className="my-4" />

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span className="font-medium">{shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tax (15%)</span>
                    <span className="font-medium">${tax.toFixed(2)}</span>
                  </div>
                  <Separator className="my-3" />
                  <div className="flex justify-between">
                    <span className="font-medium">Total</span>
                    <span className="font-bold text-emerald-600">${total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <ShieldCheck className="h-4 w-4 text-emerald-600" />
                    <span>Secure checkout powered by Kominote Pay</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium mb-4 flex items-center gap-2">
                  <Clock className="h-5 w-5 text-emerald-600" />
                  Estimated Delivery
                </h2>
                <p className="text-sm text-gray-600 mb-4">Standard delivery: 3-5 business days</p>
                <div className="flex items-center gap-2 text-sm text-emerald-600">
                  <Check className="h-4 w-4" />
                  <span>All items in stock and ready to ship</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer - Simplified for this page */}
      <footer className="bg-white border-t py-8 mt-12">
        <div className="container px-4 md:px-6">
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger */}
      <Messenger variant="light" />
    </div>
  )
}

