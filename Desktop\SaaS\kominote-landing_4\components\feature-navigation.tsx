"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Play, ShoppingBag, Users, Calendar, Briefcase, Home } from "lucide-react"
import { cn } from "@/lib/utils"

type FeatureType = "home" | "streaming" | "shopping" | "community" | "events" | "jobs"

interface FeatureNavigationProps {
  currentFeature: FeatureType
  colorScheme?: "blue" | "emerald" | "purple" | "amber" | "red" | "gray"
}

export default function FeatureNavigation({ currentFeature, colorScheme = "gray" }: FeatureNavigationProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [scrollY, setScrollY] = useState(0)

  // Handle scroll events to hide/show navigation based on scroll direction
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      setIsVisible(scrollY > currentScrollY || currentScrollY < 50)
      setScrollY(currentScrollY)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [scrollY])

  // Color mappings for different feature pages
  const colorMappings = {
    blue: {
      bg: "bg-blue-100",
      hover: "hover:bg-blue-200",
      text: "text-blue-600",
      border: "border-blue-200",
      hoverText: "text-blue-800",
    },
    emerald: {
      bg: "bg-emerald-100",
      hover: "hover:bg-emerald-200",
      text: "text-emerald-600",
      border: "border-emerald-200",
      hoverText: "text-emerald-800",
    },
    purple: {
      bg: "bg-purple-100",
      hover: "hover:bg-purple-200",
      text: "text-purple-600",
      border: "border-purple-200",
      hoverText: "text-purple-800",
    },
    amber: {
      bg: "bg-amber-100",
      hover: "hover:bg-amber-200",
      text: "text-amber-600",
      border: "border-amber-200",
      hoverText: "text-amber-800",
    },
    red: {
      bg: "bg-red-100",
      hover: "hover:bg-red-200",
      text: "text-red-600",
      border: "border-red-200",
      hoverText: "text-red-800",
    },
    gray: {
      bg: "bg-gray-100",
      hover: "hover:bg-gray-200",
      text: "text-gray-600",
      border: "border-gray-200",
      hoverText: "text-gray-800",
    },
  }

  const colors = colorMappings[colorScheme]

  const features = [
    {
      type: "home" as FeatureType,
      name: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
      bgColor: "bg-gray-100",
      hoverBgColor: "hover:bg-gray-200",
      textColor: "text-gray-600",
      hoverTextColor: "text-gray-800",
    },
    {
      type: "streaming" as FeatureType,
      name: "Streaming",
      href: "/streaming",
      icon: <Play className="h-5 w-5" />,
      bgColor: "bg-blue-100",
      hoverBgColor: "hover:bg-blue-200",
      textColor: "text-blue-600",
      hoverTextColor: "text-blue-800",
    },
    {
      type: "shopping" as FeatureType,
      name: "Shopping",
      href: "/shopping",
      icon: <ShoppingBag className="h-5 w-5" />,
      bgColor: "bg-emerald-100",
      hoverBgColor: "hover:bg-emerald-200",
      textColor: "text-emerald-600",
      hoverTextColor: "text-emerald-800",
    },
    {
      type: "community" as FeatureType,
      name: "Community",
      href: "/community",
      icon: <Users className="h-5 w-5" />,
      bgColor: "bg-purple-100",
      hoverBgColor: "hover:bg-purple-200",
      textColor: "text-purple-600",
      hoverTextColor: "text-purple-800",
    },
    {
      type: "events" as FeatureType,
      name: "Events",
      href: "/events",
      icon: <Calendar className="h-5 w-5" />,
      bgColor: "bg-amber-100",
      hoverBgColor: "hover:bg-amber-200",
      textColor: "text-amber-600",
      hoverTextColor: "text-amber-800",
    },
    {
      type: "jobs" as FeatureType,
      name: "Jobs",
      href: "/jobs",
      icon: <Briefcase className="h-5 w-5" />,
      bgColor: "bg-red-100",
      hoverBgColor: "hover:bg-red-200",
      textColor: "text-red-600",
      hoverTextColor: "text-red-800",
    },
  ]

  // Filter out current feature
  const filteredFeatures = features.filter((feature) => feature.type !== currentFeature)

  return (
    <div
      className={cn(
        "fixed left-0 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 pl-1",
        isVisible ? "translate-x-0" : "-translate-x-full",
      )}
    >
      <div
        className={cn(
          "flex flex-col gap-2 p-2 bg-white/90 backdrop-blur-sm border rounded-r-lg shadow-md",
          colors.border,
        )}
      >
        {filteredFeatures.map((feature) => (
          <Link key={feature.type} href={feature.href} className="group relative flex items-center">
            <div
              className={cn(
                "h-10 w-10 rounded-full flex items-center justify-center transition-colors",
                feature.bgColor,
                feature.hoverBgColor,
              )}
            >
              <span className={feature.textColor}>{feature.icon}</span>
            </div>
            <span
              className={cn(
                "absolute left-12 bg-white/90 backdrop-blur-sm font-medium px-3 py-1.5 rounded shadow-md border whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity",
                colors.border,
                feature.hoverTextColor,
              )}
            >
              {feature.name}
            </span>
          </Link>
        ))}
      </div>
    </div>
  )
}

