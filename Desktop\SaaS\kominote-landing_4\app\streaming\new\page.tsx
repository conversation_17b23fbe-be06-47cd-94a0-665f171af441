"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, Play } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function NewAndPopularPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link href="/streaming" className="text-sm font-medium text-gray-300 hover:text-white">
                Home
              </Link>
              <Link href="/streaming/series" className="text-sm font-medium text-gray-300 hover:text-white">
                Series
              </Link>
              <Link href="/streaming/movies" className="text-sm font-medium text-gray-300 hover:text-white">
                Movies
              </Link>
              <Link href="/streaming/new" className="text-sm font-medium text-white hover:text-blue-400">
                New & Popular
              </Link>
              <Link href="/streaming/mylist" className="text-sm font-medium text-gray-300 hover:text-white">
                My List
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative hidden md:flex items-center">
              <Search className="absolute left-2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search new content..."
                className="w-[220px] pl-8 bg-black/20 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white md:hidden">
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
            <UserNav />
          </div>
        </div>
      </header>

      <main>
        {/* Hero Banner */}
        <section className="relative h-[50vh] w-full">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            fill
            alt="New & Popular"
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-12 space-y-4">
            <div className="max-w-lg">
              <Badge className="mb-4 bg-blue-600">New & Popular</Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Fresh Local Content</h1>
              <p className="text-lg text-gray-200 mb-6">
                Discover the newest releases and most popular content from Seychellois creators.
              </p>
            </div>
          </div>
        </section>

        {/* Filter and Search Section */}
        <section className="py-6 px-4 md:px-12 bg-gray-900">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="relative w-full md:w-auto">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search new content..."
                className="w-full md:w-[300px] pl-10 bg-gray-800 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <div className="flex items-center gap-3 w-full md:w-auto">
              <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white">
                <Filter className="mr-2 h-4 w-4" /> Filter
              </Button>
              <select className="bg-gray-800 border border-gray-700 rounded-md text-sm p-2 text-gray-300">
                <option>Sort By: Newest</option>
                <option>Sort By: Trending</option>
                <option>Sort By: Most Viewed</option>
                <option>Sort By: Highest Rated</option>
              </select>
            </div>
          </div>
        </section>

        {/* Content Tabs */}
        <section className="py-8 px-4 md:px-12">
          <Tabs defaultValue="new" className="w-full">
            <TabsList className="bg-gray-800/60 mb-6">
              <TabsTrigger value="new" className="data-[state=active]:bg-blue-600">
                New Releases
              </TabsTrigger>
              <TabsTrigger value="trending" className="data-[state=active]:bg-blue-600">
                Trending Now
              </TabsTrigger>
              <TabsTrigger value="top10" className="data-[state=active]:bg-blue-600">
                Top 10 in Seychelles
              </TabsTrigger>
              <TabsTrigger value="coming" className="data-[state=active]:bg-blue-600">
                Coming Soon
              </TabsTrigger>
            </TabsList>

            <TabsContent value="new" className="mt-0">
              {/* New Series */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">New Series</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "praslin-stories",
                      title: "Praslin Stories",
                      type: "series",
                      genre: "Drama",
                      date: "Apr 15, 2023",
                    },
                    {
                      id: "island-beats",
                      title: "Island Beats",
                      type: "series",
                      genre: "Cultural",
                      date: "Apr 10, 2023",
                    },
                    {
                      id: "creole-comedy-hour",
                      title: "Creole Comedy Hour",
                      type: "series",
                      genre: "Comedy",
                      date: "Apr 5, 2023",
                    },
                    {
                      id: "la-digue-diaries",
                      title: "La Digue Diaries",
                      type: "series",
                      genre: "Drama",
                      date: "Apr 1, 2023",
                    },
                    {
                      id: "conservation-heroes",
                      title: "Conservation Heroes",
                      type: "series",
                      genre: "Documentary",
                      date: "Mar 25, 2023",
                    },
                  ].map((content) => (
                    <NewContentCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      date={content.date}
                    />
                  ))}
                </div>
              </div>

              {/* New Movies */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">New Movies</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                      date: "Apr 20, 2023",
                    },
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy", date: "Apr 12, 2023" },
                    {
                      id: "festival-of-lights",
                      title: "Festival of Lights",
                      type: "movie",
                      genre: "Cultural",
                      date: "Apr 8, 2023",
                    },
                    {
                      id: "hidden-beaches",
                      title: "Hidden Beaches",
                      type: "movie",
                      genre: "Documentary",
                      date: "Apr 3, 2023",
                    },
                    {
                      id: "island-dreams",
                      title: "Island Dreams",
                      type: "movie",
                      genre: "Drama",
                      date: "Mar 28, 2023",
                    },
                  ].map((content) => (
                    <NewContentCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      date={content.date}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="trending" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Trending This Week</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "seychelles-untold",
                      title: "Seychelles Untold",
                      type: "series",
                      genre: "Documentary",
                      trend: "Up 3",
                    },
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      type: "movie",
                      genre: "Documentary",
                      trend: "Up 1",
                    },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                      trend: "Up 5",
                    },
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama", trend: "Up 2" },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                      trend: "New",
                    },
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural", trend: "Up 4" },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      type: "movie",
                      genre: "Documentary",
                      trend: "Down 1",
                    },
                    { id: "praslin-stories", title: "Praslin Stories", type: "series", genre: "Drama", trend: "New" },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural", trend: "New" },
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy", trend: "New" },
                  ].map((content) => (
                    <TrendingCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      trend={content.trend}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="top10" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Top 10 in Seychelles</h2>
                </div>
                <div className="space-y-4">
                  {[
                    {
                      id: "seychelles-untold",
                      title: "Seychelles Untold",
                      type: "series",
                      genre: "Documentary",
                      views: "125K",
                    },
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      type: "movie",
                      genre: "Documentary",
                      views: "112K",
                    },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                      views: "98K",
                    },
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama", views: "87K" },
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural", views: "76K" },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      type: "movie",
                      genre: "Documentary",
                      views: "72K",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                      views: "65K",
                    },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural", views: "58K" },
                    {
                      id: "the-last-fisherman",
                      title: "The Last Fisherman",
                      type: "movie",
                      genre: "Drama",
                      views: "52K",
                    },
                    { id: "bird-island", title: "Bird Island", type: "series", genre: "Documentary", views: "49K" },
                  ].map((content, index) => (
                    <TopTenCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      views={content.views}
                      rank={index + 1}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="coming" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Coming Soon</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "underwater-seychelles",
                      title: "Underwater Seychelles",
                      type: "series",
                      genre: "Documentary",
                      date: "May 15, 2023",
                    },
                    {
                      id: "island-cuisine",
                      title: "Island Cuisine",
                      type: "series",
                      genre: "Cultural",
                      date: "May 22, 2023",
                    },
                    { id: "paradise-lost", title: "Paradise Lost", type: "movie", genre: "Drama", date: "Jun 1, 2023" },
                    {
                      id: "seychelles-wildlife",
                      title: "Seychelles Wildlife",
                      type: "series",
                      genre: "Documentary",
                      date: "Jun 10, 2023",
                    },
                    {
                      id: "beach-vacation",
                      title: "Beach Vacation",
                      type: "movie",
                      genre: "Comedy",
                      date: "Jun 15, 2023",
                    },
                    {
                      id: "island-adventures",
                      title: "Island Adventures",
                      type: "series",
                      genre: "Adventure",
                      date: "Jun 25, 2023",
                    },
                    { id: "creole-tales", title: "Creole Tales", type: "series", genre: "Drama", date: "Jul 5, 2023" },
                    {
                      id: "tropical-storms",
                      title: "Tropical Storms",
                      type: "movie",
                      genre: "Documentary",
                      date: "Jul 15, 2023",
                    },
                    {
                      id: "island-romance",
                      title: "Island Romance",
                      type: "movie",
                      genre: "Romance",
                      date: "Jul 22, 2023",
                    },
                    {
                      id: "seychelles-history",
                      title: "Seychelles History",
                      type: "series",
                      genre: "Documentary",
                      date: "Aug 1, 2023",
                    },
                  ].map((content) => (
                    <ComingSoonCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      date={content.date}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container px-4 md:px-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-400">Seychelles' premier streaming platform for local content</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Browse</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/streaming/series" className="text-sm text-gray-400 hover:text-white">
                    Series
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/movies" className="text-sm text-gray-400 hover:text-white">
                    Movies
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/new" className="text-sm text-gray-400 hover:text-white">
                    New & Popular
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/mylist" className="text-sm text-gray-400 hover:text-white">
                    My List
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Help</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Devices
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Terms of Use
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Cookie Preferences
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Corporate Information
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-xs text-gray-400">
              &copy; {new Date().getFullYear()} Kominote Streaming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="streaming" colorScheme="blue" />
    </div>
  )
}

// New Content Card Component
function NewContentCard({ id, title, type, genre, date }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      <Badge className="absolute top-2 left-2 bg-blue-600">NEW</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-gray-400">Added: {date}</span>
        </div>
      </div>
      {/* Clickable overlay for the entire card */}
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

// Trending Card Component
function TrendingCard({ id, title, type, genre, trend }) {
  const isTrendingUp = trend.includes("Up") || trend === "New"
  const trendColor = isTrendingUp ? "text-green-500" : "text-red-500"

  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      <Badge className={`absolute top-2 left-2 ${trend === "New" ? "bg-blue-600" : "bg-gray-800"}`}>{trend}</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className={`text-xs ${trendColor}`}>Trending: {trend}</span>
        </div>
      </div>
      {/* Clickable overlay for the entire card */}
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

// Top Ten Card Component
function TopTenCard({ id, title, type, genre, views, rank }) {
  return (
    <div className="group relative flex items-center bg-gray-800/40 hover:bg-gray-800/60 rounded-lg overflow-hidden transition-colors">
      <div className="flex-shrink-0 w-16 md:w-24 flex items-center justify-center">
        <span className="text-4xl md:text-6xl font-bold text-gray-700 group-hover:text-blue-600 transition-colors">
          {rank}
        </span>
      </div>
      <div className="relative h-20 w-36 md:h-24 md:w-44 flex-shrink-0">
        <Image src="/placeholder.svg?height=180&width=320" fill alt={title} className="object-cover" />
      </div>
      <div className="p-4 flex-grow">
        <h3 className="font-medium">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-gray-400">{views} views</span>
        </div>
      </div>
      <div className="p-4 flex-shrink-0">
        <Link href={`/streaming/watch/${id}`}>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
            <Play className="h-4 w-4 mr-1" /> Play
          </Button>
        </Link>
      </div>
    </div>
  )
}

// Coming Soon Card Component
function ComingSoonCard({ id, title, type, genre, date }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110 grayscale group-hover:grayscale-0"
      />
      <Badge className="absolute top-2 left-2 bg-amber-600">COMING SOON</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-amber-400">Release: {date}</span>
        </div>
      </div>
      {/* Clickable overlay for the entire card */}
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

