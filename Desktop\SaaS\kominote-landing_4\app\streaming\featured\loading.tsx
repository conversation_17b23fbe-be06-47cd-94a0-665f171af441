import { Skeleton } from "@/components/ui/skeleton"

export default function FeaturedLoading() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header Skeleton */}
      <div className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Skeleton className="h-8 w-32 bg-gray-800" />
            <div className="hidden md:flex gap-6">
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={i} className="h-4 w-16 bg-gray-800" />
                ))}
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-10 rounded-full bg-gray-800" />
          </div>
        </div>
      </div>

      {/* Hero Banner Skeleton */}
      <div className="relative h-[50vh] w-full bg-gray-800">
        <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-12 space-y-4">
          <div className="max-w-lg">
            <Skeleton className="h-6 w-24 mb-4 bg-gray-700" />
            <Skeleton className="h-12 w-3/4 mb-4 bg-gray-700" />
            <Skeleton className="h-20 w-full mb-6 bg-gray-700" />
          </div>
        </div>
      </div>

      {/* Filter Section Skeleton */}
      <div className="py-6 px-4 md:px-12 bg-gray-900">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <Skeleton className="h-10 w-full md:w-[300px] bg-gray-800" />
          <div className="flex items-center gap-3 w-full md:w-auto">
            <Skeleton className="h-10 w-24 bg-gray-800" />
            <Skeleton className="h-10 w-32 bg-gray-800" />
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="py-8 px-4 md:px-12">
        {/* Tabs Skeleton */}
        <div className="mb-6">
          <div className="flex gap-2">
            {Array(4)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-10 w-24 bg-gray-800" />
              ))}
          </div>
        </div>

        {/* Editor's Picks Skeleton */}
        <div className="mb-10">
          <Skeleton className="h-8 w-40 mb-4 bg-gray-800" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array(2)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-64 w-full bg-gray-800 rounded-lg" />
              ))}
          </div>
        </div>

        {/* Award Winners Skeleton */}
        <div className="mb-10">
          <Skeleton className="h-8 w-40 mb-4 bg-gray-800" />
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-64 w-full bg-gray-800 rounded-md" />
              ))}
          </div>
        </div>

        {/* Staff Picks Skeleton */}
        <div className="mb-10">
          <Skeleton className="h-8 w-40 mb-4 bg-gray-800" />
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-64 w-full bg-gray-800 rounded-md" />
              ))}
          </div>
        </div>
      </div>

      {/* Footer Skeleton */}
      <div className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container px-4 md:px-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {Array(4)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="space-y-4">
                  <Skeleton className="h-6 w-32 bg-gray-800" />
                  <div className="space-y-2">
                    {Array(4)
                      .fill(0)
                      .map((_, j) => (
                        <Skeleton key={j} className="h-4 w-24 bg-gray-800" />
                      ))}
                  </div>
                </div>
              ))}
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <Skeleton className="h-4 w-64 mx-auto bg-gray-800" />
          </div>
        </div>
      </div>
    </div>
  )
}

