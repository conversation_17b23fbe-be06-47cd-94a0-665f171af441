"""
Models for landing app.

Handles landing page content management, hero sections, features, and testimonials.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

User = get_user_model()


class HeroSection(models.Model):
    """
    Hero section content for the landing page.
    """
    title = models.CharField(
        max_length=200,
        help_text="Main hero title"
    )
    subtitle = models.CharField(
        max_length=300,
        blank=True,
        help_text="Hero subtitle or tagline"
    )
    description = models.TextField(
        max_length=500,
        blank=True,
        help_text="Hero description text"
    )

    # Call-to-action
    cta_text = models.CharField(
        max_length=50,
        default="Get Started",
        help_text="Call-to-action button text"
    )
    cta_url = models.URLField(
        blank=True,
        help_text="Call-to-action button URL"
    )

    # Media
    background_image = models.ImageField(
        upload_to='landing/hero/',
        null=True,
        blank=True,
        help_text="Hero background image"
    )
    background_video = models.FileField(
        upload_to='landing/hero/videos/',
        null=True,
        blank=True,
        help_text="Hero background video (optional)"
    )

    # Display settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this hero section is currently active"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order (lower numbers appear first)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_hero_sections'
    )

    class Meta:
        db_table = 'landing_herosection'
        verbose_name = 'Hero Section'
        verbose_name_plural = 'Hero Sections'
        ordering = ['display_order', '-created_at']

    def __str__(self):
        return f"Hero: {self.title}"


class Feature(models.Model):
    """
    Platform features to highlight on the landing page.
    """
    FEATURE_CATEGORIES = [
        ('streaming', 'Streaming'),
        ('shopping', 'Shopping'),
        ('community', 'Community'),
        ('events', 'Events'),
        ('jobs', 'Jobs'),
        ('general', 'General'),
    ]

    title = models.CharField(
        max_length=100,
        help_text="Feature title"
    )
    description = models.TextField(
        max_length=300,
        help_text="Feature description"
    )
    category = models.CharField(
        max_length=20,
        choices=FEATURE_CATEGORIES,
        default='general',
        help_text="Feature category"
    )

    # Media
    icon = models.ImageField(
        upload_to='landing/features/icons/',
        null=True,
        blank=True,
        help_text="Feature icon"
    )
    image = models.ImageField(
        upload_to='landing/features/',
        null=True,
        blank=True,
        help_text="Feature image"
    )

    # Links
    learn_more_url = models.URLField(
        blank=True,
        help_text="Learn more link"
    )

    # Display settings
    is_featured = models.BooleanField(
        default=False,
        help_text="Show on main landing page"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this feature is active"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'landing_feature'
        verbose_name = 'Feature'
        verbose_name_plural = 'Features'
        ordering = ['display_order', 'title']

    def __str__(self):
        return f"{self.title} ({self.get_category_display()})"


class Testimonial(models.Model):
    """
    User testimonials for the landing page.
    """
    name = models.CharField(
        max_length=100,
        help_text="Testimonial author name"
    )
    title = models.CharField(
        max_length=100,
        blank=True,
        help_text="Author title or position"
    )
    company = models.CharField(
        max_length=100,
        blank=True,
        help_text="Author company"
    )
    content = models.TextField(
        max_length=500,
        help_text="Testimonial content"
    )

    # Rating
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        default=5,
        help_text="Rating out of 5 stars"
    )

    # Media
    avatar = models.ImageField(
        upload_to='landing/testimonials/',
        null=True,
        blank=True,
        help_text="Author avatar"
    )

    # Location (Seychelles context)
    location = models.CharField(
        max_length=100,
        blank=True,
        help_text="Author location (e.g., Mahé, Praslin)"
    )

    # Display settings
    is_featured = models.BooleanField(
        default=False,
        help_text="Show on main landing page"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this testimonial is active"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_testimonials'
    )

    class Meta:
        db_table = 'landing_testimonial'
        verbose_name = 'Testimonial'
        verbose_name_plural = 'Testimonials'
        ordering = ['display_order', '-created_at']

    def __str__(self):
        return f"Testimonial by {self.name}"

    @property
    def star_rating(self):
        """Return star rating as string."""
        return '★' * self.rating + '☆' * (5 - self.rating)


class FAQ(models.Model):
    """
    Frequently Asked Questions for the landing page.
    """
    CATEGORIES = [
        ('general', 'General'),
        ('streaming', 'Streaming'),
        ('shopping', 'Shopping'),
        ('community', 'Community'),
        ('events', 'Events'),
        ('jobs', 'Jobs'),
        ('account', 'Account'),
        ('technical', 'Technical'),
    ]

    question = models.CharField(
        max_length=200,
        help_text="FAQ question"
    )
    answer = models.TextField(
        help_text="FAQ answer"
    )
    category = models.CharField(
        max_length=20,
        choices=CATEGORIES,
        default='general',
        help_text="FAQ category"
    )

    # Display settings
    is_featured = models.BooleanField(
        default=False,
        help_text="Show on main landing page"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this FAQ is active"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Statistics
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this FAQ was viewed"
    )
    helpful_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times marked as helpful"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_faqs'
    )

    class Meta:
        db_table = 'landing_faq'
        verbose_name = 'FAQ'
        verbose_name_plural = 'FAQs'
        ordering = ['display_order', 'question']

    def __str__(self):
        return f"FAQ: {self.question[:50]}..."


class NewsUpdate(models.Model):
    """
    News and updates for the platform.
    """
    UPDATE_TYPES = [
        ('feature', 'New Feature'),
        ('improvement', 'Improvement'),
        ('bugfix', 'Bug Fix'),
        ('announcement', 'Announcement'),
        ('event', 'Event'),
        ('maintenance', 'Maintenance'),
    ]

    title = models.CharField(
        max_length=200,
        help_text="Update title"
    )
    content = models.TextField(
        help_text="Update content"
    )
    update_type = models.CharField(
        max_length=20,
        choices=UPDATE_TYPES,
        default='announcement',
        help_text="Type of update"
    )

    # Media
    featured_image = models.ImageField(
        upload_to='landing/news/',
        null=True,
        blank=True,
        help_text="Featured image for the update"
    )

    # Links
    read_more_url = models.URLField(
        blank=True,
        help_text="Link to full article or details"
    )

    # Display settings
    is_featured = models.BooleanField(
        default=False,
        help_text="Show on main landing page"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this update is active"
    )

    # Publishing
    publish_date = models.DateTimeField(
        default=timezone.now,
        help_text="When to publish this update"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_news'
    )

    class Meta:
        db_table = 'landing_newsupdate'
        verbose_name = 'News Update'
        verbose_name_plural = 'News Updates'
        ordering = ['-publish_date']

    def __str__(self):
        return f"{self.get_update_type_display()}: {self.title}"

    @property
    def is_published(self):
        """Check if the update is published."""
        return self.publish_date <= timezone.now() and self.is_active


class ContactMessage(models.Model):
    """
    Contact form messages from the landing page.
    """
    MESSAGE_TYPES = [
        ('general', 'General Inquiry'),
        ('support', 'Support Request'),
        ('business', 'Business Inquiry'),
        ('partnership', 'Partnership'),
        ('feedback', 'Feedback'),
        ('bug_report', 'Bug Report'),
    ]

    name = models.CharField(
        max_length=100,
        help_text="Sender name"
    )
    email = models.EmailField(
        help_text="Sender email"
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Sender phone number"
    )
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPES,
        default='general',
        help_text="Type of message"
    )
    subject = models.CharField(
        max_length=200,
        help_text="Message subject"
    )
    message = models.TextField(
        help_text="Message content"
    )

    # Status
    is_read = models.BooleanField(
        default=False,
        help_text="Whether the message has been read"
    )
    is_replied = models.BooleanField(
        default=False,
        help_text="Whether the message has been replied to"
    )

    # Response
    response = models.TextField(
        blank=True,
        help_text="Admin response to the message"
    )
    responded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contact_responses'
    )
    responded_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the response was sent"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'landing_contactmessage'
        verbose_name = 'Contact Message'
        verbose_name_plural = 'Contact Messages'
        ordering = ['-created_at']

    def __str__(self):
        return f"Message from {self.name}: {self.subject}"
