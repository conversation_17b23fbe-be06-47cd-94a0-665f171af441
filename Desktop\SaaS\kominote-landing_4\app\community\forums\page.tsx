import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  MessageSquare,
  Bell,
  Filter,
  Plus,
  Globe,
  Leaf,
  Compass,
  Music,
  Camera,
  Utensils,
  Palette,
  Landmark,
  Heart,
  Sailboat,
} from "lucide-react"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function ForumsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search discussions, groups, or users..."
                className="pl-10 pr-4 py-2 w-full border-purple-200 focus:border-purple-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                5
              </Badge>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <MessageSquare className="h-5 w-5" />
              <span className="sr-only">Messages</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                3
              </Badge>
            </Button>
            <UserNav />
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/community"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Home
            </Link>
            <Link
              href="/community/forums"
              className="text-sm font-medium whitespace-nowrap text-purple-600 border-b-2 border-purple-600 pb-1"
            >
              Forums
            </Link>
            <Link
              href="/community/groups"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Groups
            </Link>
            <Link
              href="/community/members"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Members
            </Link>
            <Link
              href="/community/activity"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Activity
            </Link>
          </nav>
        </div>
      </header>

      <main className="container px-4 py-8">
        {/* Forums Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Discussion Forums</h1>
            <p className="text-gray-600 mt-1">Join conversations on various topics with the Seychelles community</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2 border-purple-200 text-purple-600 hover:bg-purple-50">
              <Filter className="h-4 w-4" /> Filter
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700 gap-2">
              <Plus className="h-4 w-4" /> Create New Topic
            </Button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-8 border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search forums and topics..."
                className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-purple-500"
              />
            </div>
            <div className="flex items-center gap-2">
              <select className="flex-1 text-sm border rounded-md px-2 py-2 bg-white">
                <option>All Categories</option>
                <option>Local Culture & Traditions</option>
                <option>Island Living</option>
                <option>Environmental Conservation</option>
                <option>Tourism & Travel</option>
                <option>Arts & Entertainment</option>
              </select>
              <Button className="bg-purple-600 hover:bg-purple-700">Search</Button>
            </div>
          </div>
        </div>

        {/* Forums Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <ForumCard
            title="Local Culture & Traditions"
            description="Discuss Seychellois culture, traditions, language, and heritage."
            topics={156}
            posts={1243}
            icon={<Globe className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Island Living"
            description="Share tips and experiences about daily life in Seychelles."
            topics={124}
            posts={987}
            icon={<Home className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Environmental Conservation"
            description="Discuss initiatives to protect Seychelles' unique environment."
            topics={98}
            posts={876}
            icon={<Leaf className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Tourism & Travel"
            description="Share travel tips, destinations, and experiences in Seychelles."
            topics={87}
            posts={754}
            icon={<Compass className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Arts & Entertainment"
            description="Discuss local music, art, films, and entertainment."
            topics={76}
            posts={623}
            icon={<Music className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Photography"
            description="Share and discuss photos of Seychelles' beautiful landscapes."
            topics={65}
            posts={512}
            icon={<Camera className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Food & Cuisine"
            description="Exchange recipes and discuss Seychellois and Creole cuisine."
            topics={54}
            posts={401}
            icon={<Utensils className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Creative Corner"
            description="Share your creative works, from writing to crafts and more."
            topics={43}
            posts={321}
            icon={<Palette className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Local News & Events"
            description="Discuss current events and news from around Seychelles."
            topics={32}
            posts={254}
            icon={<Landmark className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Relationships & Family"
            description="Discuss relationships, family life, and parenting in Seychelles."
            topics={21}
            posts={187}
            icon={<Heart className="h-6 w-6 text-purple-600" />}
          />
          <ForumCard
            title="Water Sports & Activities"
            description="Discuss sailing, diving, fishing, and other water activities."
            topics={19}
            posts={165}
            icon={<Sailboat className="h-6 w-6 text-purple-600" />}
          />
          <div className="bg-purple-50 rounded-lg border border-purple-200 p-6 flex flex-col items-center justify-center text-center">
            <Plus className="h-8 w-8 text-purple-600 mb-2" />
            <h3 className="font-bold text-lg text-purple-800">Suggest a Forum</h3>
            <p className="text-sm text-purple-600 mt-1 mb-4">Have an idea for a new discussion forum? Let us know!</p>
            <Button className="bg-purple-600 hover:bg-purple-700">Submit Suggestion</Button>
          </div>
        </div>

        {/* Forum Guidelines */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
          <h2 className="text-xl font-bold mb-4">Forum Guidelines</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <p className="text-gray-600">
                Our forums are designed to foster respectful and engaging discussions. Please follow these guidelines:
              </p>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">Be respectful and considerate in all interactions</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">Stay on topic within each forum</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">Share authentic and relevant content</span>
                </li>
              </ul>
            </div>
            <div className="space-y-3">
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">No spam, scams, or misleading content</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">Protect your privacy and respect others' privacy</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <Check className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-600">Report any violations to community moderators</span>
                </li>
              </ul>
              <div className="flex justify-end mt-4">
                <Button variant="outline" className="border-purple-200 text-purple-600 hover:bg-purple-50">
                  Read Full Guidelines
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier community platform connecting people with shared interests.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Community</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/community/forums" className="text-sm text-gray-600 hover:text-purple-600">
                    Forums
                  </Link>
                </li>
                <li>
                  <Link href="/community/groups" className="text-sm text-gray-600 hover:text-purple-600">
                    Groups
                  </Link>
                </li>
                <li>
                  <Link href="/community/members" className="text-sm text-gray-600 hover:text-purple-600">
                    Members
                  </Link>
                </li>
                <li>
                  <Link href="/community/activity" className="text-sm text-gray-600 hover:text-purple-600">
                    Activity
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Guidelines
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Safety Tips
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Moderation
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Community Standards
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Community. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="community" colorScheme="purple" prominentSectionHeight={300} />
    </div>
  )
}

// Forum Card Component
function ForumCard({ title, description, topics, posts, icon }) {
  return (
    <Link href={`/community/forums/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-200 hover:shadow-md border border-transparent hover:border-purple-200 h-full">
        <div className="flex items-center gap-3 mb-3">
          <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center group-hover:bg-purple-200 transition-colors">
            {icon}
          </div>
          <h3 className="font-bold text-lg group-hover:text-purple-600 transition-colors">{title}</h3>
        </div>
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">{description}</p>
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>{topics} topics</span>
          <span>{posts} posts</span>
        </div>
      </div>
    </Link>
  )
}

function Home(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  )
}

function Check(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  )
}

